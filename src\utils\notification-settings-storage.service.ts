/**
 * Notification Settings Storage Service
 *
 * Simple file-based storage for notification settings using a unified JSON file.
 * Uses a single config/notification-settings.json file with default system settings.
 * Individual user settings are handled by the application logic with fallback to defaults.
 *
 * <AUTHOR> Ireland Development Team
 * @version 4.0.0
 * @since 2025-07-18
 */

import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import { LoggerService } from './logger.service';

export interface NotificationSettingsData {
  id: string;
  user_id: string;
  agent_assigned: boolean;
  case_status_update: boolean;
  agent_query: boolean;
  document_rejection: boolean;
  missing_document_reminder_days: number;
  system_maintenance: boolean;
  final_decision_issued: boolean;
  created_at: string;
  updated_at: string;
}

@Injectable()
export class NotificationSettingsStorageService {
  private readonly logger = new Logger(NotificationSettingsStorageService.name);
  private readonly configDir = path.join(process.cwd(), 'config');
  private readonly unifiedSettingsFile = path.join(
    this.configDir,
    'notification-settings.json',
  );

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * Get the unified notification settings file path
   */
  private getUnifiedSettingsFilePath(): string {
    return this.unifiedSettingsFile;
  }

  /**
   * Ensure config directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
    } catch (error) {
      // Log error but don't throw - directory might already exist
      this.loggerService.error(
        `Failed to create config directory: ${error.message}`,
        error.stack,
        'NotificationSettingsStorageService',
      );
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get current ISO date string
   */
  private getCurrentISOString(): string {
    return new Date().toISOString();
  }

  /**
   * Log error to file for debugging
   */
  private logError(message: string, error?: any): void {
    this.loggerService.error(
      message,
      error?.stack || error,
      'NotificationSettingsStorageService',
    );
  }

  /**
   * Read notification settings from unified configuration file
   * Returns default settings with user_id populated for the requested user
   */
  async readSettings(userId: string): Promise<NotificationSettingsData | null> {
    try {
      await this.ensureConfigDirectory();
      const filePath = this.getUnifiedSettingsFilePath();
      const data = await fs.readFile(filePath, 'utf8');
      const settings = JSON.parse(data) as NotificationSettingsData;

      // Validate that the settings object has the expected structure
      if (!settings.id) {
        this.logError(`Invalid settings structure in unified config file`);
        return null;
      }

      // Return settings with the requested user_id
      return {
        ...settings,
        user_id: userId,
        updated_at: this.getCurrentISOString(),
      };
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return null (not an error)
        return null;
      }

      this.logError(`Failed to read unified settings: ${error.message}`, error);
      return null;
    }
  }

  /**
   * Write notification settings to unified configuration file
   * Updates the system default settings (user-specific settings are handled in memory)
   */
  async writeSettings(
    userId: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    try {
      await this.ensureConfigDirectory();
      const filePath = this.getUnifiedSettingsFilePath();

      // For unified config, we update the system defaults
      // Individual user preferences would be handled by the application layer
      const settingsToWrite = {
        ...data,
        user_id: 'system', // Keep as system for unified config
        updated_at: this.getCurrentISOString(),
      };

      await fs.writeFile(
        filePath,
        JSON.stringify(settingsToWrite, null, 2),
        'utf8',
      );

      this.logger.log(
        `Successfully updated unified settings (requested by user: ${userId})`,
      );
    } catch (error) {
      this.logError(
        `Failed to write unified settings: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update notification settings (merge with existing)
   * This ensures PUT operations preserve existing fields
   */
  async updateSettings(
    userId: string,
    updates: Partial<NotificationSettingsData>,
  ): Promise<NotificationSettingsData> {
    const existingData = await this.readSettings(userId);

    // If no existing data, create default settings merged with updates
    if (!existingData) {
      const defaultSettings = this.createDefaultSettings(userId);
      const mergedData = {
        ...defaultSettings,
        ...updates,
        updated_at: this.getCurrentISOString(),
      } as NotificationSettingsData;

      await this.writeSettings(userId, mergedData);
      return mergedData;
    }

    // Merge updates with existing data, preserving all existing fields
    const mergedData = {
      ...existingData,
      ...updates,
      updated_at: this.getCurrentISOString(),
    } as NotificationSettingsData;

    await this.writeSettings(userId, mergedData);
    return mergedData;
  }

  /**
   * Create default notification settings
   */
  createDefaultSettings(userId: string): NotificationSettingsData {
    const currentTime = this.getCurrentISOString();
    return {
      id: this.generateId(),
      user_id: userId,
      agent_assigned: true,
      case_status_update: true,
      agent_query: true,
      document_rejection: true,
      missing_document_reminder_days: 7,
      system_maintenance: true,
      final_decision_issued: true,
      created_at: currentTime,
      updated_at: currentTime,
    };
  }

  /**
   * Delete notification settings (resets to system defaults)
   * In unified config approach, this doesn't delete individual user settings
   * but could reset the unified config to defaults
   */
  async deleteSettings(userId: string): Promise<void> {
    try {
      // In unified config approach, we don't delete individual user files
      // This method could reset the unified config to defaults if needed
      this.logger.log(
        `Delete settings requested for user ${userId} - using unified config, no individual file to delete`,
      );
    } catch (error) {
      this.logError(
        `Failed to process delete settings for user ${userId}: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check if unified settings file exists
   */
  async settingsExist(_userId: string): Promise<boolean> {
    try {
      const filePath = this.getUnifiedSettingsFilePath();
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
